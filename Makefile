PROJECT_NAME := onr_django_ninja_app
PYTHON_VERSION := 3.11.0
VENV_NAME := onr_django_ninja-$(PYTHON_VERSION)

help:
	@fgrep -h "##" $(MAKEFILE_LIST) | sed -e 's/\(\:.*\#\#\)/\:\ /' | fgrep -v fgrep | sed -e 's/\\$$//' | sed -e 's/##//'

create-venv: ## install dev requirements
	python -m venv .venv

setup-dev:
	pip install -r requirements/dev.txt

setup-prod:
	pip install -r requirements.txt

migrations-dev:
	python manage.py makemigrations

migrate-dev:
	python manage.py migrate

create-superuser:
	python manage.py createsuperuser

run-dev:
	python manage.py runserver 8001

build: ## up all containers and building the project image
	docker-compose up -d --build

up: ## up all containers
	docker-compose up -d

down: ## down all containers
	docker-compose down
	docker-compose rm

recreate: down up ## recreate containers

migrations: ## create alembic migratrion file
	docker exec -it $(PROJECT_NAME) python manage.py makemigrations

migrate: ## run migratrion
	docker exec -it $(PROJECT_NAME) python manage.py migrate

logs: ## project logs on container
	docker logs $(PROJECT_NAME) --follow

lint: ruff

ruff:
	ruff check --fix --show-fixes .
	ruff format .

test: ## running test
	pytest -v

test-coverage: ## running test with coverage
	pytest -v --cov-config=setup.cfg --cov=src --cov-report=term-missing --cov-report=html --cov-report=xml --cov-fail-under=90
	sed -i 's/<source>.*<\/source>/<source>.\/<\/source>/g' coverage.xml

flake8: ## running flake8
	echo "Running flake8"
	flake8 src

### Redis ###
run-redis:
	python manage.py createcachetable