from app.application.interfaces.attachment_repository_interface import AttachmentRepositoryInterface
from app.domain.entities.attachment import Attachment
from app.infrastructure.framework.django.apps.attachment.models import Attachment as AttachmentModel
from app.errors.types.http_not_found import HttpNotFoundError


class AttachmentRepository(AttachmentRepositoryInterface):

    @staticmethod
    def add(attachment: Attachment) -> Attachment:
        new_attachment = AttachmentModel(**attachment.model_dump(exclude_none=True))
        new_attachment.save()
        attachment.id = new_attachment.id
        return attachment

    @staticmethod
    def get_by_request_id(request_id: int) -> Attachment:
        try:
            attachment_model = AttachmentModel.objects.get(pedido_id=request_id)
            return Attachment.model_validate(attachment_model)
        except AttachmentModel.DoesNotExist:
            pass

    def update(self, attachment: Attachment) -> Attachment:
        attachment_model = self.__get_by_id(attachment_id=attachment.id)
        for key, value in attachment.model_dump(exclude={"id"}).items():
            setattr(attachment_model, key, value)
        attachment_model.save()
        return Attachment.model_validate(attachment_model)

    def delete(self, attachment_id: int):
        attachment_to_delete = self.__get_by_id(attachment_id=attachment_id)
        attachment_to_delete.delete()

    @staticmethod
    def __get_by_id(attachment_id: int) -> AttachmentModel:
        try:
            requester = AttachmentModel.objects.get(id=attachment_id)
        except AttachmentModel.DoesNotExist:
            raise HttpNotFoundError("PaymentMethod não encontrado")

        return requester
