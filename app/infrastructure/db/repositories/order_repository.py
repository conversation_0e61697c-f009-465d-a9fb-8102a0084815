from typing import List
from uuid import UUID

from django.db.models import Q, <PERSON>
from django.core.paginator import <PERSON>ginator
from app.application.interfaces.order_repository_interface import OrderRepositoryInterface
from app.domain.dtos.get_orders_dto import GetOrdersDTOIn, GetOrdersDTOPage, OrderDTO
from app.domain.dtos.get_status_quantity_dto import GetStatusQuantityDTOIn
from app.domain.entities.order import Order
from app.domain.entities.process import Process
from app.domain.entities.process_part import ProcessPart
from app.infrastructure.framework.django.apps.order.models import Order as OrderModel
from app.infrastructure.framework.django.apps.property.models import Property as PropertyModel
from app.errors.types import HttpNotFoundError
from app.errors.types import HttpBadRequestError
from app.domain.entities.user_constriction import UserConstriction
from app.domain.enums.order_status_enum import OrderStatusEnum


class OrderRepository(OrderRepositoryInterface):

    def add(self, order: Order) -> Order:

        new_order = OrderModel(**order.model_dump(exclude={"id", "slug"}, exclude_unset=True))
        new_order.save()
        return Order.model_validate(new_order)

    def update(self, order: Order) -> Order:
        order_model = self.__get_by_slug(order.slug)
        if order_model.cns != order.cns:
            raise HttpBadRequestError("Nao é possivel mudar CNS de uma ordem")

        for key, value in order.model_dump(exclude={"id", "slug", "criado_por"}, exclude_unset=True).items():
            setattr(order_model, key, value)

        order_model.save()

        return Order.model_validate(order_model)

    @staticmethod
    def get_by_request_id(request_id: int) -> List[Order]:
        orders = OrderModel.objects.filter(pedido_id=request_id)
        return [Order.model_validate(ordem) for ordem in orders]

    @staticmethod
    def get_by_parts(parts: List[ProcessPart]) -> List[Order]:
        if parts is not None and len(parts) > 0:
            part_ids = list(map(lambda x: x.id, parts))
            properties = PropertyModel.objects.filter(parte__in=part_ids).select_related('ordem')
            result = [Order.model_validate(p.ordem) for p in properties]
            return result
        return []

    @staticmethod
    def __get_by_slug(order_slug: UUID) -> Order:
        try:
            order = OrderModel.objects.get(slug=order_slug)
        except OrderModel.DoesNotExist:
            raise HttpNotFoundError("Ordem nao encontrada")

        return order

    @staticmethod
    def get_by_cns_and_request_id(request_id: int, cns: str) -> Order:
        try:
            order = OrderModel.objects.get(pedido_id=request_id, cns=cns)
            return order
        except OrderModel.DoesNotExist:
            pass

    def delete(self, order: Order):
        order_in = self.__get_by_slug(order_slug=order.slug)
        order_in.delete()

    def get(self, order_id: int) -> Order:
        try:
            order_model = OrderModel.objects.get(id=order_id)
            order = Order.model_validate(order_model)
        except OrderModel.DoesNotExist:
            raise HttpNotFoundError("Ordem nao encontrada")

        return order

    @staticmethod
    def __exclude_forbidden_status_unless_sent(status_list) -> list[int]:
        permitted_status = []
        if (status_list is None or len(status_list) == 0):
            permitted_status = [status.value for status in OrderStatusEnum.allowed_by_default()]
            return permitted_status
        if (len(status_list) > 0):
            permitted_status = status_list
        return permitted_status

    def get_by_filter_intersection(self, filters: GetOrdersDTOIn) -> GetOrdersDTOPage:
        pagina = filters.pagina
        total_itens_pagina = filters.total_itens_pagina

        filter_dict = filters.model_dump(exclude_none=True, exclude_unset=True, exclude={'total_itens_pagina',
                                                                                         'pagina',
                                                                                         'ordem_status_id_list',
                                                                                         'user_list',
                                                                                         'uf_oab',
                                                                                         'ordens_lidas',
                                                                                         'numero_oab',
                                                                                         'filtro_default'})

        status_id_list = self.__exclude_forbidden_status_unless_sent(filters.ordem_status_id_list)

        query = Q()
        filter_mappings = {
            'tipo_constricao_list': 'pedido__tipo_constricao_id__in',
            'protocolo': 'orderprotocol__protocolo__exact',
            'numero_processo': 'numero_processo__exact',
            'process_ids': 'processo_id__in',
            'data_inicio': 'criado_em__date__gte',
            'data_fim': 'criado_em__date__lte',
        }
        funnel_filters = Q()
        for key, val in filter_dict.items():
            funnel_filters &= Q(**{filter_mappings[key]: val})

        if filters.user_list:
            funnel_filters &= Q(criado_por__in=filters.user_list)
        else:
            funnel_filters &= Q(pedido__solicitante__numero_oab=filters.numero_oab,
                                pedido__solicitante__uf_oab=filters.uf_oab.upper())

        funnel_filters &= Q(ordem_status_id__in=status_id_list)
        funnel_filters &= Q(orderprotocol__isnull=False)

        if filters.ordens_lidas is not None:
            funnel_filters &= Q(lido=filters.ordens_lidas)

        query = funnel_filters

        orders = (OrderModel.objects.filter(query)
                  .annotate(ultima_atualizacao=Max('orderstatusrel__criado_em'))
                  .distinct()
                  .order_by('-ultima_atualizacao'))

        paginator = Paginator(orders, total_itens_pagina)
        page_orders = paginator.get_page(pagina)

        complete_orders = GetOrdersDTOPage(
            ordens=[OrderDTO.model_validate(order) for order in page_orders],
            pagina=page_orders.number,
            total_paginas=paginator.num_pages,
            total_itens=paginator.count,
            total_itens_pagina=total_itens_pagina
        )

        return complete_orders

    def get_by_filter_union(self, filters: GetOrdersDTOIn) -> GetOrdersDTOPage:
        pagina = filters.pagina
        total_itens_pagina = filters.total_itens_pagina

        filter_dict = filters.model_dump(exclude_none=True, exclude_unset=True, exclude={'total_itens_pagina',
                                                                                         'pagina',
                                                                                         'ordem_status_id_list',
                                                                                         'user_list',
                                                                                         'uf_oab',
                                                                                         'ordens_lidas',
                                                                                         'numero_oab',
                                                                                         'filtro_default'})

        status_id_list = self.__exclude_forbidden_status_unless_sent(filters.ordem_status_id_list)

        query = Q()
        filter_mappings = {
            'tipo_constricao_list': 'pedido__tipo_constricao_id__in',
            'protocolo': 'orderprotocol__protocolo__exact',
            'numero_processo': 'numero_processo__exact',
            'process_ids': 'processo_id__in',
            'data_inicio': 'criado_em__date__gte',
            'data_fim': 'criado_em__date__lte',
        }
        funnel_filters = Q()
        for key, val in filter_dict.items():
            funnel_filters &= Q(**{filter_mappings[key]: val})

        if filters.user_list:
            funnel_filters &= Q(criado_por__in=filters.user_list)
        else:
            funnel_filters &= Q(pedido__solicitante__numero_oab=filters.numero_oab,
                                pedido__solicitante__uf_oab=filters.uf_oab.upper())

        funnel_filters &= Q(ordem_status_id__in=status_id_list)
        funnel_filters &= Q(orderprotocol__isnull=False)

        # Filtro pelo atributo lido
        lido_filter = Q()
        if filters.ordens_lidas is not None:
            lido_filter = Q(lido=filters.ordens_lidas)
            lido_filter &= Q(orderprotocol__isnull=False)

            if filters.user_list:
                lido_filter &= Q(criado_por__in=filters.user_list)
            else:
                lido_filter &= Q(pedido__solicitante__numero_oab=filters.numero_oab,
                                 pedido__solicitante__uf_oab=filters.uf_oab.upper())


        # Unindo os dois conjuntos de registros com OR
        query = funnel_filters | lido_filter if filters.ordens_lidas is not None else funnel_filters

        orders = (OrderModel.objects.filter(query)
                  .annotate(ultima_atualizacao=Max('orderstatusrel__criado_em'))
                  .distinct()
                  .order_by('-ultima_atualizacao'))
        paginator = Paginator(orders, total_itens_pagina)
        page_orders = paginator.get_page(pagina)

        complete_orders = GetOrdersDTOPage(
            ordens=[OrderDTO.model_validate(order) for order in page_orders],
            pagina=page_orders.number,
            total_paginas=paginator.num_pages,
            total_itens=paginator.count,
            total_itens_pagina=total_itens_pagina
        )

        return complete_orders


    def count_by_status(self, data_in: GetStatusQuantityDTOIn) -> int:
        try:
            query = Q()
            query &= Q(ordem_status=data_in.status_id)
            query &= Q(orderprotocol__isnull=False)

            if data_in.user_list:
                query &= Q(criado_por__in=data_in.user_list)
            else:
                query &= Q(pedido__solicitante__uf_oab=data_in.uf_oab.upper(),
                           pedido__solicitante__numero_oab=data_in.numero_oab)

            count = OrderModel.objects.filter(query).count()

            return count
        except OrderModel.DoesNotExist:
            count = 0

    def get_by_order_protocol(self, order_protocol: str) -> Order:
        try:
            order = OrderModel.objects.get(orderprotocol__protocolo=order_protocol)
            return Order.model_validate(order)
        except OrderModel.DoesNotExist:
            raise HttpNotFoundError("Ordem nao encontrada")

    def get_paid_orders(self) -> List[Order]:
        ordens = OrderModel.objects.filter(ordem_status__in=[OrderStatusEnum.PAGAMENTO_EFETIVADO_PRENOTACAO.value,
                                                             OrderStatusEnum.PENDENTE_ENVIO_CARTORIO.value],
                                           orderprotocol__isnull=False)
        return [Order.model_validate(ordem) for ordem in ordens]

    def get_by_process(self, process: Process) -> list[Order]:
        orders_model = OrderModel.objects.filter(processo__id=process.id)
        orders = [Order.model_validate(o) for o in orders_model]
        return orders

    def get_by_slug(self, order_slug: UUID) -> Order:
        order = self.__get_by_slug(order_slug=order_slug)
        return Order.model_validate(order)

    def get_by_order_status(self, order_status: OrderStatusEnum) -> list[Order]:
        order_models = OrderModel.objects.filter(ordem_status_id=order_status.value)
        return [Order.model_validate(order) for order in order_models]

    def get_by_process_with_protocol(self, process: Process) -> list[Order]:
        orders_model = OrderModel.objects.filter(processo__id=process.id,
                                                 orderprotocol__protocolo__isnull=False).annotate(
            ultima_atualizacao=Max('orderstatusrel__criado_em')).order_by('-ultima_atualizacao')
        orders = [Order.model_validate(o) for o in orders_model]
        return orders

    def get_by_bank_slip_number(self, bank_slip_number: str) -> Order:
        try:
            order_model = OrderModel.objects.filter(
                costs__paymentcosts__pagamento__numero_boleto=bank_slip_number
            ).distinct().get()
            return Order.model_validate(order_model)
        except OrderModel.DoesNotExist:
            raise HttpNotFoundError("Ordem nao encontrada para o boleto informado")

    def set_read_order_by_order_id(self, order_id: int, read: bool):
        quantity = OrderModel.objects.filter(id=order_id).update(lido=read)
        if quantity == 0:
            raise HttpNotFoundError("Ordem não encontrada")
