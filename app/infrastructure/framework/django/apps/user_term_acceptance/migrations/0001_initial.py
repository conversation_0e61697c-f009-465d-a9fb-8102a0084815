# Generated by Django 5.0.6 on 2024-08-13 04:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("term", "0001_initial"),
        ("user_constriction", "0002_userconstriction_user_const_cpf_slug_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserTermAcceptance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("acceptance_date", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="term.term"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user_constriction.userconstriction",
                    ),
                ),
            ],
            options={
                "db_table": "aceite_termo",
                "ordering": ("id",),
            },
        ),
    ]
