from abc import ABC, abstractmethod
from app.domain.entities.response_status_attachment import ResponseStatusAttachment


class ResponseStatusAttachmentRepositoryInterface(ABC):

    @staticmethod
    @abstractmethod
    def add_batch(response_status_attachment_list: list[ResponseStatusAttachment]) -> list[ResponseStatusAttachment]:
        pass

    @staticmethod
    @abstractmethod
    def filter_by_response_status_id(response_status_id: int) -> list[ResponseStatusAttachment]:
        pass

