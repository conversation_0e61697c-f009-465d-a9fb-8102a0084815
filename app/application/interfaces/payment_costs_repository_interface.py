from abc import ABC, abstractmethod

from app.domain.entities.payment import Payment
from app.domain.entities.payment_costs import PaymentCosts


class PaymentCostsRepositoryInterface(ABC):
    """ Classe repositorio dos pagamentos das custas """

    @abstractmethod
    def get_by_costs_id(self, costs_id: int) -> PaymentCosts:
        pass

    def get_latest_by_costs_id(self, costs_id: int) -> PaymentCosts:
        pass

    @staticmethod
    @abstractmethod
    def add(payment_cost: PaymentCosts) -> PaymentCosts:
        pass

    @abstractmethod
    def get_by_payments(self, payments: list[Payment]) -> list[PaymentCosts]:
        pass

    @abstractmethod
    def update_batch(self, payment_costs: list[PaymentCosts]) -> int:
        pass

    @abstractmethod
    def add_batch(self, payment_costs: list[PaymentCosts]) -> list[PaymentCosts]:
        pass

    @abstractmethod
    def get_latest_payment_cost_ids(self, payments: list[Payment]) -> list[PaymentCosts]:
        """Devolve pra cada pagamento o mais recente objeto payment_cost relacionado"""
        pass

    @abstractmethod
    def get_most_recent_paid_by_costs_id(self, costs_id: int) -> PaymentCosts:
        pass
