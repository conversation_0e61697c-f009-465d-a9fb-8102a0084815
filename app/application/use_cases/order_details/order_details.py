from app.application.interfaces.attachment_repository_interface import AttachmentRepositoryInterface
from app.application.interfaces.order_repository_interface import OrderRepositoryInterface
from app.application.interfaces.payment_method_repository_interface import PaymentMethodRepositoryInterface
from app.application.interfaces.process_part_repository_interface import ProcessPartRepositoryInterface
from app.application.interfaces.process_repository_interface import ProcessRepositoryInterface
from app.application.interfaces.property_repository_interface import PropertyRepositoryInterface
from app.application.interfaces.request_repository_interface import RequestRepositoryInterface
from app.application.interfaces.requester_repository_interface import RequesterRepositoryInterface
from app.application.interfaces.certificate_mandate_file_repository_interface import CertificateMandateFileRepositoryInterface
from app.application.interfaces.user_constriction_repository_interface import UserConstrictionRepositoryInterface
from app.domain.dtos.order_details_confirmation_dto import (OrderDetailsConfirmationDTO,
                                                            ProcessDTO,
                                                            OrderRequirementNote,
                                                            OrderRegistration,
                                                            RequesterDTO,
                                                            PaymentMethodDTO,
                                                            PropertyDTO,
                                                            PartsDTO, UserDTO)
from app.domain.dtos.get_orders_dto import File
from app.domain.entities.attachment import Attachment
from app.domain.entities.process_part import ProcessPart
from app.domain.entities.payment_method import PaymentMethod
from app.domain.entities.process import Process
from app.domain.entities.property import Property
from app.domain.entities.requester import Requester
from app.domain.entities.order import Order
from app.domain.entities.user_constriction import UserConstriction
from app.domain.enums.nature_of_process_enum import NatureOfProcessEnum
from app.domain.enums.type_payment_method_enum import TypePaymentMethodEnum
from app.domain.enums.type_quality_part_enum import TypeQualityPartEnum
from app.domain.enums.type_constriction_enum import TypeConstrictionEnum
from app.domain.enums.order_status_enum import OrderStatusEnum
from app.domain.use_cases.order_details.order_details import OrderDetails as OrderDetailsInterface
from app.domain.enums.type_response_enum import TypeResponseEnum
from app.infrastructure.db.repositories.response_status_attachment_repository import \
    ResponseStatusAttachmentRepositoryInterface
from app.application.interfaces.response_status_repository_interface import ResponseStatusRepositoryInterface
from app.application.interfaces.requirement_note_response_repository_interface import RequirementNoteResponseRepositoryInterface
from app.application.interfaces.order_status_rel_repository_interface import OrderStatusRelRepositoryInterface
from app.domain.dtos.order_details_confirmation_dto import StatusUpdateHistory
import pytz
from app.infrastructure.utils import helpers as h


class OrderDetails(OrderDetailsInterface):
    def __init__(self,
                 order_repository: OrderRepositoryInterface,
                 process_repository: ProcessRepositoryInterface,
                 requester_repository: RequesterRepositoryInterface,
                 request_repository: RequestRepositoryInterface,
                 property_repository: PropertyRepositoryInterface,
                 payment_method_repository: PaymentMethodRepositoryInterface,
                 attachment_repository: AttachmentRepositoryInterface,
                 process_part_repository: ProcessPartRepositoryInterface,
                 certificate_mandate_file_repository: CertificateMandateFileRepositoryInterface,
                 user_constriction_repository_interface: UserConstrictionRepositoryInterface,
                 response_status_repository: ResponseStatusRepositoryInterface,
                 response_status_attachment_repository: ResponseStatusAttachmentRepositoryInterface,
                 requirement_note_response_repository:RequirementNoteResponseRepositoryInterface,
                 order_status_rel_repository: OrderStatusRelRepositoryInterface):
        self.order_repository = order_repository
        self.process_repository = process_repository
        self.requester_repository = requester_repository
        self.request_repository = request_repository
        self.property_repository = property_repository
        self.payment_method_repository = payment_method_repository
        self.attachment_repository = attachment_repository
        self.process_part_repository = process_part_repository
        self.certificate_mandate_file_repository = certificate_mandate_file_repository
        self.user_constriction_repository_interface = user_constriction_repository_interface
        self.response_status_repository = response_status_repository
        self.response_status_attachment_repository = response_status_attachment_repository
        self.requirement_note_response_repository = requirement_note_response_repository
        self.order_status_rel_repository = order_status_rel_repository
        self.time_zone = pytz.timezone(h.get_time_zone())

    def get_order(self, order_protocol: str) -> OrderDetailsConfirmationDTO:
        order = self.order_repository.get_by_order_protocol(order_protocol=order_protocol)
        process = self.process_repository.get_by_process_id(process_id=order.processo_id)
        request = self.request_repository.get_by_id(request_id=order.pedido_id)
        attachment = self.attachment_repository.get_by_request_id(request_id=order.pedido_id)
        properties = self.property_repository.get_by_order_id(order_id=order.id)
        certificate_mandate = self.certificate_mandate_file_repository.get_by_request_id(request_id=order.pedido_id)
        user = self.user_constriction_repository_interface.get_by_id(user_id=order.criado_por)
        parts = self.process_part_repository.get_by_process_id(process_id=process.id)

        requester = self.requester_repository.get_by_request_id(request_id=order.pedido_id)
        payment_method = self.payment_method_repository.get_by_request_id(request_id=order.pedido_id)
        payment_method_dto = self.__get_payment_method_info(payment_method=payment_method, attachment=attachment)
        requester_dto = self.__get_requester_info(requester=requester)
        process_dto = self.__get_process_info(process=process)
        properties_dto = self.__get_properties(properties=properties, process=process)
        parts_dto = self.__get_parts(parts=parts)
        requirement_note = self._get_requirement_note(order=order)
        registration_attachment = self.__get_registration_attachment(order=order)
        status_update_history = self.order_status_rel_repository.get_history_by_order_id(order_id=order.id)
        filtered_status_update_history = self._check_if_extended(order=order, historic=status_update_history)
        user_requester_dto = self.__get_user_requester_info(user=user)

        order_details_dto = OrderDetailsConfirmationDTO(
            protocolo=order_protocol,
            ordem_slug=order.slug,
            pedido_slug=request.slug,
            cns=order.cns,
            nome_cartorio=properties[0].nome_cartorio,
            tipo_constricao_id=request.tipo_constricao_id,
            tipo_constricao=str(TypeConstrictionEnum(request.tipo_constricao_id)),
            ordem_status=str(OrderStatusEnum(order.ordem_status_id)),
            url_certidao_mandado=certificate_mandate.url,
            lido=order.lido,
            criado_em=order.criado_em,
            criado_por=user.nome,
            processo=process_dto,
            advogado_solicitante=requester_dto,
            forma_pagamento=payment_method_dto,
            imoveis=properties_dto,
            partes=parts_dto,
            data_auto_termo=request.data_auto_termo,
            nota_exigencia=requirement_note,
            registro_averbado=registration_attachment,
            historico_status=filtered_status_update_history,
            usuario_solicitante=user_requester_dto
        )

        return order_details_dto

    def __get_registration_attachment(self, order: Order) -> OrderRegistration | None:
        if order.ordem_status_id != OrderStatusEnum.REGISTRO_AVERBADO.value:
            return None

        response_status_list = self.response_status_repository.filter_by_order_id_and_tipo_resposta_id(
            order_id=order.id,
            tipo_resposta_id=TypeResponseEnum.REGISTRO_AVERBADO.value
        )

        if not response_status_list:
            return None

        response_status = response_status_list[0]

        registration_attachments = self.response_status_attachment_repository.filter_by_response_status_id(
            response_status_id=response_status.id
        )

        return OrderRegistration(
            criado_em=response_status.criado_em.astimezone(self.time_zone).date(),
            descricao=response_status.descricao,
            oe_nome_usuario=response_status.oe_nome_usuario,
            resposta_status_slug=response_status.slug,
            arquivos=[File(url=rne.url) for rne in registration_attachments]
        )

    def _get_requirement_note(self, order: Order) -> list[OrderRequirementNote] | None:
        if order.ordem_status_id != OrderStatusEnum.NOTA_DE_EXIGENCIA.value:
            return None

        response_status_list = self.response_status_repository.filter_by_order_id_and_tipo_resposta_id(
            order_id=order.id, tipo_resposta_id=TypeResponseEnum.NOTA_DE_EXIGENCIA.value)

        order_requirement_notes = []
        for response_status in response_status_list:
            response_status_attachment = self.response_status_attachment_repository.filter_by_response_status_id(
                response_status_id=response_status.id)

            requirement_note_response = self.requirement_note_response_repository.get_by_response_status_id(
                response_status_id=response_status.id)
            if requirement_note_response:
                continue

            order_requirement_notes.append(OrderRequirementNote(
                criado_em=response_status.criado_em.astimezone(self.time_zone).date(),
                descricao=response_status.descricao,
                oe_nome_usuario=response_status.oe_nome_usuario,
                resposta_status_slug=response_status.slug,
                arquivos=[File(url=rne.url) for rne in response_status_attachment]
            ))

        return order_requirement_notes

    def __get_properties(self,
                         properties: list[Property],
                         process: Process) -> list[PropertyDTO]:
        properties_dto = []
        for property_info in properties:
            part = self.process_part_repository.get_by_document_and_process_id(process_id=process.id,
                                                                document=property_info.documento_parte)

            properties_dto.append(
                PropertyDTO(
                    imovel_slug=property_info.slug,
                    cns=property_info.cns,
                    uf=property_info.uf,
                    comarca=property_info.comarca,
                    comarca_nome=property_info.comarca_nome,
                    documento_imovel=property_info.documento_imovel,
                    cep=property_info.cep,
                    endereco=property_info.endereco,
                    complemento=property_info.complemento,
                    bairro=property_info.bairro,
                    municipio=property_info.municipio,
                    municipio_nome=property_info.municipio_nome,
                    parte_possui_totalidade_imovel=property_info.parte_possui_totalidade_imovel,
                    constricao_atinge_totalidade_imovel=property_info.constricao_atinge_totalidade_imovel,
                    percentual_constricao=property_info.percentual_constricao,
                    percentual_executado=property_info.percentual_executado,
                    data_decisao_pc=property_info.data_decisao_pc,
                    folha_pc=property_info.folha_pc,
                    parte_proprietario_titular=property_info.parte_proprietario_titular,
                    documento_parte=property_info.documento_parte,
                    nome_parte=part.nome,
                    documento_proprietario_rp=property_info.documento_proprietario_rp,
                    nome_proprietario_rp=property_info.nome_proprietario_rp,
                    data_decisao_rp=property_info.data_decisao_rp,
                    folhas_rp=property_info.folhas_rp
                ))

        return properties_dto

    @staticmethod
    def __get_payment_method_info(payment_method: PaymentMethod,
                                  attachment: Attachment) -> PaymentMethodDTO | None:
        if not payment_method:
            return None

        anexo_despacho = None
        if attachment:
            anexo_despacho = attachment.url

        payment_method_dto = PaymentMethodDTO(
            tipo_forma_pagamento_id=payment_method.tipo_forma_pagamento_id,
            tipo_forma_pagamento=str(TypePaymentMethodEnum(payment_method.tipo_forma_pagamento_id)),
            juizado_especial_civil=payment_method.juizado_especial_civil,
            data_decisao=payment_method.data_decisao,
            folhas=payment_method.folhas,
            fundamento_legal=payment_method.fundamento_legal,
            anexo_despacho=anexo_despacho
        )

        return payment_method_dto

    @staticmethod
    def __get_requester_info(requester: Requester) -> RequesterDTO | None:
        if not requester:
            return None

        requester_dto = RequesterDTO(
            nome=requester.nome,
            telefone=requester.telefone,
            email=requester.email,
            numero_oab=requester.numero_oab,
            uf_oab=requester.uf_oab
        )

        return requester_dto

    @staticmethod
    def __get_process_info(process: Process) -> ProcessDTO:
        process_dto = ProcessDTO(
            numero_processo=process.numero_processo,
            segredo_justica=process.segredo_justica,
            valor_divida=process.valor_divida,
            natureza_processo=str(NatureOfProcessEnum(process.natureza_processo_id)))

        return process_dto

    @staticmethod
    def __get_parts(parts: list[ProcessPart]) -> list[PartsDTO]:

        parts_dto_list = [PartsDTO(
                documento=part.documento,
                nome=part.nome,
                tipo_qualidade_id=part.tipo_qualidade_id,
                tipo_qualidade=str(TypeQualityPartEnum(part.tipo_qualidade_id))
            ) for part in parts]

        return parts_dto_list

    def _check_if_extended(self,
                           order: Order,
                           historic: list[StatusUpdateHistory]) -> list[StatusUpdateHistory]:
        extended_response = self.response_status_repository.filter_by_order_id_and_tipo_resposta_id(
            order_id=order.id,
            tipo_resposta_id=TypeResponseEnum.PRENOTACAO_PRORROGADA.value
        )

        if extended_response:
            for history in historic:
                if history.ordem_status_id == OrderStatusEnum.PRENOTADO.value:
                    history.prenotacao_prorrogada = True
                    break

        return historic

    def __get_user_requester_info(self, user: UserConstriction) -> UserDTO:
        return UserDTO(
            nome=user.nome,
            orgao=user.orgao,
            vara=user.vara,
            cargo=user.cargo
        )
