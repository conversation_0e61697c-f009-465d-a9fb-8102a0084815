from enum import Enum


class OrderStatusEnum(Enum):

    AGUAR<PERSON><PERSON>O_PAGAMENTO_PRENOTACAO = 1
    PAGAMENTO_EFETIVADO_PRENOTACAO = 2
    PENDENTE_ENVIO_CARTORIO = 3
    EM_ABERTO = 4
    PROCESSANDO = 5
    DEVOLVIDO = 6
    PRENOTADO = 7
    NOTA_DE_EXIGENCIA = 8
    NOTA_DE_EXIGENCIA_RESPONDIDA = 9
    AGUARDANDO_PAGAMENTO = 10
    PAGAMENTO_EFETIVADO = 11
    REGISTRO_AVERBADO = 12
    FINALIZADO_SEM_PRATICA_ATO = 13
    PROCESSANDO_RESPOSTA_NOTA_EXIGENCIA = 14

    @staticmethod
    def allowed_by_default():
        allowed_status = [
            OrderStatusEnum.AGUARDANDO_PAGAMENTO_PRENOTACAO,
            OrderStatusEnum.EM_ABERTO,
            OrderStatusEnum.PROCESSANDO,
            OrderStatusEnum.PRENOTADO,
            OrderStatusEnum.NOTA_DE_EXIGENCIA,
            OrderStatusEnum.AGUARDANDO_PAGAMENTO,
            OrderStatusEnum.PAGAMENTO_EFETIVADO,
            OrderStatusEnum.NOTA_DE_EXIGENCIA_RESPONDIDA,
            OrderStatusEnum.PAGAMENTO_EFETIVADO_PRENOTACAO,
            OrderStatusEnum.PENDENTE_ENVIO_CARTORIO,
            OrderStatusEnum.PROCESSANDO_RESPOSTA_NOTA_EXIGENCIA
        ]
        return allowed_status
    
    @staticmethod
    def disabled_by_default():
        disabled_status = [
            OrderStatusEnum.DEVOLVIDO,
            OrderStatusEnum.REGISTRO_AVERBADO,
            OrderStatusEnum.FINALIZADO_SEM_PRATICA_ATO
        ]
        return disabled_status

    def __str__(self):
        descriptions = {
            OrderStatusEnum.AGUARDANDO_PAGAMENTO_PRENOTACAO: "Aguardando pagamento da prenotação",
            OrderStatusEnum.PAGAMENTO_EFETIVADO_PRENOTACAO: "Em aberto",
            OrderStatusEnum.PENDENTE_ENVIO_CARTORIO: "Em aberto",
            OrderStatusEnum.EM_ABERTO: "Em aberto",
            OrderStatusEnum.PROCESSANDO: "Processando",
            OrderStatusEnum.DEVOLVIDO: "Devolvido",
            OrderStatusEnum.PRENOTADO: "Prenotado",
            OrderStatusEnum.NOTA_DE_EXIGENCIA: "Nota de exigência",
            OrderStatusEnum.NOTA_DE_EXIGENCIA_RESPONDIDA: "Nota de exigência respondida",
            OrderStatusEnum.AGUARDANDO_PAGAMENTO: "Aguardando pagamento para registro",
            OrderStatusEnum.PAGAMENTO_EFETIVADO: "Pagamento efetivado",
            OrderStatusEnum.REGISTRO_AVERBADO: "Registro / Averbado",
            OrderStatusEnum.FINALIZADO_SEM_PRATICA_ATO: "Finalizado sem prática de ato",
            OrderStatusEnum.PROCESSANDO_RESPOSTA_NOTA_EXIGENCIA: "Nota de Exigência Respondida"
        }
        return descriptions.get(self, self.name)


