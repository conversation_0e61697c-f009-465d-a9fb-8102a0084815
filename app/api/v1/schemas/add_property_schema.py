from pydantic import BaseModel, constr, field_validator, Field, model_validator
from uuid import UUID
from typing import Optional
from datetime import date
from app.infrastructure.utils.validators import validate_uf
from decimal import Decimal
from app.infrastructure.utils.validators import DocumentValidation
from app.domain.dtos.add_property_confirmation_dto import AddPropertyConfirmationDTO


class PropertyIn(BaseModel):
    uf: constr(max_length=2, min_length=2)
    comarca: int  # cidade id
    comarca_nome: str
    cns: constr(max_length=6, min_length=6)
    nome_cartorio: str
    documento_imovel: constr(max_length=20)
    cep: Optional[str] = None
    endereco: constr(max_length=255)
    complemento: Optional[constr(max_length=200)] = None
    bairro: constr(max_length=50)
    municipio: int
    municipio_nome: str
    parte_possui_totalidade_imovel: bool
    constricao_atinge_totalidade_imovel: bool
    percentual_constricao: Optional[Decimal] = Field(None, ge=0.01, le=99.999)
    percentual_executado: Optional[Decimal] = Field(None, ge=0.01, le=99.999)
    data_decisao_pc: Optional[date] = None
    folha_pc: Optional[str] = None
    parte_proprietario_titular: bool
    documento_parte: str
    documento_proprietario_rp: Optional[str] = None
    nome_proprietario_rp: Optional[str] = None
    data_decisao_rp: Optional[date] = None
    folhas_rp: Optional[str] = None
    depositario: Optional[constr(max_length=255)] = None

    @field_validator('data_decisao_pc')
    def validate_data_decisao_pc(cls, value: date):
        if not value:
            return value
        if value > date.today():
            raise ValueError(f"data_decisao_pc nao pode ser futura")

        return value

    @field_validator('data_decisao_rp')
    def validate_data_decisao_rp(cls, value: date):
        if not value:
            return value
        if value > date.today():
            raise ValueError(f"data_decisao_rp nao pode ser futura")

        return value

    @field_validator('cep')
    def validate_cep(cls, value: str):
        if not value:
            return value
        cep = value.replace(".", "").replace("-", "")

        if cep.isalpha() or len(cep) != 8:
            raise ValueError(f"CEP Invalido")

        return cep

    @field_validator('uf')
    def validate_uf(cls, value):
        if validate_uf(value):
            return value.upper()
        else:
            raise ValueError(f"UF invalida: {value}")

    @field_validator('cns')
    def validate_cns(cls, value: str):
        if not value.isdigit():
            raise ValueError("CNS Inválida, necessário apenas caracteres númericos")
        return value

    @field_validator('documento_parte')
    def validate_document(cls, v):
        if not v.isdigit():
            raise ValueError('Apenas valores números aceitos')

        if not DocumentValidation().document_validation(document=v):
            raise ValueError('Documento Invalido')
        return v

    @field_validator('documento_proprietario_rp')
    def validate_documento_proprietario_rp(cls, v):

        if not v:
            return v

        if not v.isdigit():
            raise ValueError('Apenas valores números aceitos')

        if not DocumentValidation().document_validation(document=v):
            raise ValueError('Documento Invalido')
        return v

    @field_validator('nome_proprietario_rp')
    def convert_to_upper(cls, value):
        if isinstance(value, str):
            return value.upper()
        return value

    @model_validator(mode='before')
    def all_conditional_validations(cls, values):

        # validate_parte_propriedade_titular_rules
        parte_proprietario_titular = values.get("parte_proprietario_titular")
        documento_proprietario_rp = values.get("documento_proprietario_rp")
        nome_proprietario_rp = values.get("nome_proprietario_rp")
        data_decisao_rp = values.get("data_decisao_rp")
        folhas_rp = values.get("folhas_rp")

        if not parte_proprietario_titular:
            if not nome_proprietario_rp:
                raise ValueError("Necessario incluir nome_proprietario_rp do proprietario")
            if not data_decisao_rp:
                raise ValueError("Necessario incluir data_decisao_rp do proprietario")
            if not folhas_rp:
                raise ValueError("Necessario incluir folhas_rp do proprietario")
            if not documento_proprietario_rp:
                raise ValueError("Necessario incluir documento_proprietario_rp do proprietario")

        elif nome_proprietario_rp or data_decisao_rp or folhas_rp or documento_proprietario_rp:
            raise ValueError("Se parte_proprietario_titular for True, não enviar dados do RP")

        # validate_percent_rules

        percentual_constricao = values.get("percentual_constricao")
        percentual_executado = values.get("percentual_executado")
        parte_possui_totalidade_imovel = values.get("parte_possui_totalidade_imovel")
        constricao_atinge_totalidade_imovel = values.get("constricao_atinge_totalidade_imovel")

        if not constricao_atinge_totalidade_imovel and not percentual_constricao:
            raise ValueError("percentual_constricao obrigatorio, quando constricao_atinge_totalidade_imovel e Falso")

        if constricao_atinge_totalidade_imovel and percentual_constricao:
            raise ValueError("nao informar percentual_constricao, quando constricao_atinge_totalidade_imovel e True")

        if not parte_possui_totalidade_imovel and not percentual_executado:
            raise ValueError("percentual_executado obrigatorio, quando parte_possui_totalidade_imovel e Falso")

        if parte_possui_totalidade_imovel and percentual_executado:
            raise ValueError("nao informar percentual_executado, quando parte_possui_totalidade_imovel e True")

        # validate_data_decisao_pc_and_folha_pc_needs

        data_decisao_pc = values.get("data_decisao_pc")
        folha_pc = values.get("folha_pc")
        percentual_constricao = values.get("percentual_constricao")
        percentual_executado = values.get("percentual_executado")
        if parte_possui_totalidade_imovel:
            percentual_executado = 100
        if constricao_atinge_totalidade_imovel:
            percentual_constricao = 100

        if percentual_constricao and percentual_executado:

            if percentual_constricao > percentual_executado and (not data_decisao_pc or not folha_pc):
                raise ValueError(
                    "data_decisao_pc e folha_pc obrigatorios qunado percentual_constricao>percentual_executado")

            if percentual_constricao < percentual_executado and (data_decisao_pc or folha_pc):
                raise ValueError("nao informar data_decisao_pc e folha_pc qunado percentual_constricao <"
                                 " percentual_executado")

        return values


class ConstrictionUserIn(BaseModel):
    orgao_id: int
    orgao: Optional[str] = None
    vara_id: int
    vara: Optional[str] = None
    perfil_id: Optional[int] = None
    perfil: Optional[str] = None
    cpf_usuario: constr(min_length=11, max_length=11)
    nome_usuario: constr(max_length=100)
    cargo: Optional[str] = None

    @field_validator('cpf_usuario')
    def validate_document(cls, v):
        if not v.isdigit():
            raise ValueError('Apenas valores números aceitos')

        if not DocumentValidation().document_validation(document=v):
            raise ValueError('Documento Invalido')
        return v

    @field_validator('nome_usuario')
    def convert_to_upper(cls, value):
        if isinstance(value, str):
            return value.upper()
        return value


class AddPropertySchemaIn(BaseModel):

    numero_processo: str
    pedido_slug: UUID
    imovel: PropertyIn
    usuario: ConstrictionUserIn

    @field_validator('numero_processo')
    def validate_document(cls, v):
        # apenas devolvo o numero sem a mascara, da forma que é salvo no banco
        return v.replace(".", "").replace("-", "")


class AddPropertySchemaOut(BaseModel):
    result: AddPropertyConfirmationDTO
