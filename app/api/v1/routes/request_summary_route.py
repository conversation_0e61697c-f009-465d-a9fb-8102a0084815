from ninja import Router
from http import HTTPStatus
from app.api.v1.schemas.error_schema import UniqueError, UnprocessableEntityError
from app.errors.error_handler import ErrorHandler
from app.infrastructure.framework.django.adapters.django_request_adapter import DjangoRequestAdapter
from app.infrastructure.services.logging import Logging
from app.api.v1.schemas.request_summary_schema import RequestSummaryConfirmationSchemaOut, RequestSummarySchemaIn
from app.api.v1.composers.request_summary_composer.request_summary_composer import RequestSummaryComposer

logging = Logging()
router = Router(tags=['Pedido'])


@router.post(
        'pedidos/resumo/',
        response={
            HTTPStatus.OK: RequestSummaryConfirmationSchemaOut,
            HTTPStatus.BAD_REQUEST: UniqueError,
            HTTPStatus.INTERNAL_SERVER_ERROR: UniqueError,
            HTTPStatus.UNPROCESSABLE_ENTITY: UnprocessableEntityError,
            HTTPStatus.NOT_FOUND: UniqueError,
        },
)
def request_summary(request, process: RequestSummarySchemaIn):
    """ Resumo dos pedidos """
    try:
        controller = RequestSummaryComposer().compose()
        http_response = DjangoRequestAdapter().adapt(request, controller)
    except Exception as e:
        http_response = ErrorHandler(logging).handle_errors(e)

    return http_response.status_code, http_response.body

