from http import HTTPStatus

from ninja import Router

from app.api.v1.composers.set_read_protocol_composer.set_read_protocol_composer import SetReadProtocolComposer
from app.api.v1.schemas.error_schema import UniqueError, UnprocessableEntityError
from app.api.v1.schemas.set_read_protocol_schema import SetReadProtocolSchemaOut, SetReadProtocolSchemaIn

from app.errors.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from app.infrastructure.framework.django.adapters.django_request_adapter import DjangoRequestAdapter
from app.infrastructure.services.logging import Logging

logging = Logging()
router = Router(tags=['Ordem'])


@router.patch(
    'ordens/{protocolo}/lido',
    response={
        HTTPStatus.OK: SetReadProtocolSchemaOut,
        HTTPStatus.BAD_REQUEST: UniqueError,
        HTTPStatus.INTERNAL_SERVER_ERROR: UniqueError,
        HTTPStatus.UNPROCESSABLE_ENTITY: UnprocessableEntityError,
        HTTPStatus.CONFLICT: UniqueError,
        HTTPStatus.NOT_FOUND: UniqueError,
    },
)
def set_protocol_as_read(request, lido: SetReadProtocolSchemaIn, protocolo: str):
    try:
        controller = SetReadProtocolComposer().compose()
        http_response = DjangoRequestAdapter().adapt(request=request, controller=controller)
    except Exception as e:
        http_response = ErrorHandler(logging=logging).handle_errors(e)

    return http_response.status_code, http_response.body
