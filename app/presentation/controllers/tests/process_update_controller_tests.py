import http
import uuid
from app.domain.dtos.process_update_confirmation_dto import ProcessUpdateConfirmationDTO
from app.domain.entities.user_constriction import UserConstriction
from app.domain.enums.type_quality_part_enum import TypeQualityPartEnum
from app.domain.use_cases.process_update.process_update import ProcessUpdate
from app.presentation.controllers.process_update.process_update_controller import ProcessUpdateController
from app.presentation.http_types.http_request import HttpRequest
import pytest
from unittest.mock import Mock


class TestProcessUpdateController:

    @pytest.fixture
    def process_update_use_case_mock(self):
        """ Fixture para o use case ProcessUpdate """
        return Mock(spec=ProcessUpdate)

    @pytest.fixture
    def user_constriction_mock(self):
        """ Fixture para o user contriction """
        return Mock(spec=UserConstriction)

    @pytest.fixture
    def process_update_controller(self, process_update_use_case_mock):
        """ Fixture para a criacao do controller """
        return ProcessUpdateController(process_update_use_case_mock)

    @pytest.fixture
    def process_update_request_sample(self):
        """ Fixture do body para update """
        return {
            "numero_processo": "123456",
            "numero_processo_padronizado": False,
            "nome_processo_id": 1,
            "segredo_justica": False,
            "valor_divida": 1500,
            "orgao_id": 1,
            "orgao": "Teste",
            "vara_id": 1,
            "perfil_id": 1,
            "perfil": "Teste",
            "cpf_usuario": "77263358077",
            "nome_usuario": "Fulano",
            "partes": [
                {"documento": "77263358077",
                 "nome": "Fulano 2",
                 "tipo_qualidade_id": TypeQualityPartEnum.EXECUTADO.value,
                 "passivo_constricao": False,
                 },
                {"documento": "71343552095",
                 "nome": "Fulano 2",
                 "tipo_qualidade_id": TypeQualityPartEnum.EXEQUENTE.value,
                 "passivo_constricao": False,
                 }
            ]
        }

    @pytest.fixture
    def http_request_sample(self, process_update_request_sample):
        """ Sample de um HttpRequest """
        return HttpRequest(body=process_update_request_sample, path_params={'processo_slug':'8c8ed0cd-50d6-4dd8-b71a-6decbade1c57'})

    @pytest.fixture
    def process_update_return_sample(self, process_update_request_sample):
        """ Fixture para o return do controller """
        return ProcessUpdateConfirmationDTO(numero_processo=process_update_request_sample.get('numero_processo'), slug=uuid.uuid4())

    def test_process_update_sucessful(self,
                                      http_request_sample,
                                      process_update_use_case_mock,
                                      process_update_controller,
                                      process_update_return_sample):
        # Set-Up
        process_update_use_case_mock.update.return_value = process_update_return_sample

        # Act
        result = process_update_controller.handle(http_request_sample)

        # Assert
        assert result.body.get('result') == process_update_return_sample.model_dump()
        process_update_use_case_mock.update.assert_called_once()

    def test_process_update_process_invalid_input(self,
                                      process_update_use_case_mock,
                                      process_update_controller,
                                      process_update_return_sample):
        # Set-Up
        process_update_use_case_mock.update.return_value = process_update_return_sample

        # Act and Assert
        with pytest.raises(Exception):
            process_update_controller.handle(HttpRequest(body={"invalid_field": "invalid_value"}))

    def test_process_update_use_case_threw_exception(self,
                                      http_request_sample,
                                      process_update_use_case_mock,
                                      process_update_controller):
        # Set-Up
        process_update_use_case_mock.update.side_effect = Exception('Processo não encontrado')

        # Act and Assert
        with pytest.raises(Exception, match=('Processo não encontrado')):
            process_update_controller.handle(http_request_sample)

